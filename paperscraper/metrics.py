"""
Metrics utilities to compute and print statistics about affiliation processing results.

Implements the statistics printed by printStatistics in the notebook.

Each record in `affiliations` input is expected to include keys:
- input_tokens (int)
- output_tokens (int)
- total_processing_time (float seconds)
- first_page_processing_time (float seconds)
- affiliation_processing_time (float seconds)
- paper_source ("html"|"pdf"|"none")
"""
from __future__ import annotations

from typing import Dict, List, Tuple


def _avg(values: List[float]) -> float:
    return round(sum(values) / len(values), 2) if values else 0.0


def getAverageInputTokens(affiliations: List[Dict]) -> float:
    return _avg([a.get("input_tokens", 0) for a in affiliations])


def getAverageOutputTokens(affiliations: List[Dict]) -> float:
    return _avg([a.get("output_tokens", 0) for a in affiliations])


def getAverageTotalProcessingTime(affiliations: List[Dict]) -> float:
    return _avg([a.get("total_processing_time", 0.0) for a in affiliations])


def getAverageFirstPageProcessingTime(affiliations: List[Dict]) -> float:
    return _avg([a.get("first_page_processing_time", 0.0) for a in affiliations])


def getAverageAffiliationProcessingTime(affiliations: List[Dict]) -> float:
    return _avg([a.get("affiliation_processing_time", 0.0) for a in affiliations])


def getSourceCounts(affiliations: List[Dict]) -> Dict[str, int]:
    counts: Dict[str, int] = {}
    for a in affiliations:
        src = a.get("paper_source", "none")
        counts[src] = counts.get(src, 0) + 1
    return counts


def getAverageTokensPerSource(affiliations: List[Dict]) -> Tuple[Dict[str, float], Dict[str, float]]:
    source_counts = getSourceCounts(affiliations)
    source_input_totals: Dict[str, int] = {}
    source_output_totals: Dict[str, int] = {}

    for a in affiliations:
        src = a.get("paper_source", "none")
        source_input_totals[src] = source_input_totals.get(src, 0) + int(a.get("input_tokens", 0))
        source_output_totals[src] = source_output_totals.get(src, 0) + int(a.get("output_tokens", 0))

    source_avg_input = {src: round(source_input_totals[src] / count, 2) for src, count in source_counts.items() if count > 0}
    source_avg_output = {src: round(source_output_totals[src] / count, 2) for src, count in source_counts.items() if count > 0}
    return source_avg_input, source_avg_output


def getCost(affiliations: List[Dict], price_per_input_token: float = 0.05 / 1_000_000, price_per_output_token: float = 0.4 / 1_000_000) -> float:
    total_cost = 0.0
    for a in affiliations:
        total_cost += a.get("input_tokens", 0) * price_per_input_token + a.get("output_tokens", 0) * price_per_output_token
    return round(total_cost, 6)


def printStatistics(affiliations: List[Dict]) -> None:
    print("Number of affiliations: ", len(affiliations))
    print("Average input tokens: ", getAverageInputTokens(affiliations))
    print("Average output tokens: ", getAverageOutputTokens(affiliations))
    print("Average total processing time: ", getAverageTotalProcessingTime(affiliations))
    print("Average first page processing time: ", getAverageFirstPageProcessingTime(affiliations))
    print("Average affiliation processing time: ", getAverageAffiliationProcessingTime(affiliations))

    print("Source counts: ")
    src_counts = getSourceCounts(affiliations)
    for source, count in src_counts.items():
        print(f"\t{source} \t{count}")

    source_avg_input, source_avg_output = getAverageTokensPerSource(affiliations)
    print("Average tokens per source:")
    for source in source_avg_input:
        print(f"\t{source}\t{source_avg_input[source]}\t{source_avg_output[source]}")

    print("Total cost: ", getCost(affiliations))
