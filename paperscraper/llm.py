"""
LLM processing for extracting affiliations.

Provides getAffiliations that uses the OpenAI client with selectable provider:
- provider="openai": base_url from OPENAI_BASE_URL env var (default: https://api.openai.com/v1), requires OPENAI_API_KEY
- provider="ollama": base_url from OLLAMA_BASE_URL env var (default: http://localhost:11434/v1), api_key can be any string

Default models:
- OpenAI: gpt-5-nano (can be overridden with AFFILIATIONS_MODEL env var)
- Ollama: qwen3:4b-instruct (can be overridden with AFFILIATIONS_MODEL env var)

Returns a dict with input_tokens, output_tokens, and affiliations (pydantic model serialized to dict).
"""
from __future__ import annotations

from typing import Dict, Optional, List
from pydantic import BaseModel
from openai import OpenAI
import os
import json
import re
import logging
from dotenv import load_dotenv
from tqdm import tqdm

load_dotenv()

# Load organizations once at module import time
with open("orgs.txt", "r", encoding="utf-8") as f:
    ORGS_LIST = f.read().strip()


class AuthorAffiliation(BaseModel):
    author_name: str
    organization: str


class AllAuthorAffiliation(BaseModel):
    author_name: str
    organization: str
    is_notable: bool


class PaperAffilationAnalysis(BaseModel):
    paper_title: str
    all_affiliations: List[AllAuthorAffiliation]
    notable_affiliations: List[AuthorAffiliation]
    has_notable_affiliations: bool


# Get base URLs from environment variables with fallback defaults
OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434/v1")
OPENAI_BASE = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

# Default models for each provider
DEFAULT_OPENAI_MODEL = "gpt-5-nano"
DEFAULT_OLLAMA_MODEL = "qwen3:4b-instruct"


def _get_client(provider: str = "openai", api_key: Optional[str] = None, base_url: Optional[str] = None) -> OpenAI:
    provider = (provider or "openai").lower()
    if provider == "ollama":
        return OpenAI(base_url=base_url or OLLAMA_BASE, api_key="ollama")
    # default openai
    key = api_key or os.getenv("OPENAI_API_KEY")
    if not key:
        raise RuntimeError("OPENAI_API_KEY is required for provider 'openai'.")
    return OpenAI(base_url=base_url or OPENAI_BASE, api_key=key)

def parse_output(completion, provider):

    if provider in {"openai", "ollama"}:
        return {
            "input_tokens": completion.usage.prompt_tokens,
            "output_tokens": completion.usage.completion_tokens,
            "affiliations": completion.choices[0].message.parsed.model_dump()
        }

    return {}


def getAffiliations(paper_title: str, paper_text: str, *, provider: str = "openai", api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None) -> Dict:
    # Use global organizations list loaded once at module import
    valid_organizations = ORGS_LIST

    prompt = f"""
You are tasked with extracting ALL author affiliations from a research paper and determining which ones match the provided notable organizations.

Paper title: {paper_title}

First page text: {paper_text}

Notable organizations to check against:
{valid_organizations}

Extract ALL author affiliations from the paper, then:
1. Mark each affiliation as notable (is_notable: true) if it matches any organization in the provided list
2. Include all affiliations in all_affiliations
3. Include only notable affiliations in notable_affiliations
4. Set has_notable_affiliations to true if any affiliations match the notable organizations list

Return comprehensive affiliation data for all authors.
"""

    client = _get_client(provider=provider, api_key=api_key, base_url=base_url)

    # Choose model based on provider if not specified
    if model:
        model = model
    elif provider.lower() == "ollama":
        model = DEFAULT_OLLAMA_MODEL
    elif provider.lower() == "openai":
        model = DEFAULT_OPENAI_MODEL
    else:
        raise ValueError(f"Unsupported provider: {provider}")
    
    completion = client.beta.chat.completions.parse(
        model=model,
        messages=[
            {"role": "system", "content": "Extract ALL author affiliations from research papers and identify which match notable organizations."},
            {"role": "user", "content": prompt},
        ],
        response_format=PaperAffilationAnalysis,
    )

    response = parse_output(completion, provider)

    return response

def getAffiliationsBatch(papers: List[dict], *, provider: str = "openai", api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None, max_workers: Optional[int] = None) -> tuple[List[Dict], int]:
    """
    Process multiple papers for affiliation extraction using ThreadPoolExecutor.

    Args:
        papers: List of paper dictionaries, each containing 'title' and 'first_page_text' fields
        provider: LLM provider ("openai" or "ollama")
        api_key: API key for the provider (optional, uses env vars)
        base_url: Base URL for the provider (optional, uses defaults)
        model: Model name to use (optional, uses provider defaults)
        max_workers: Maximum number of worker threads (default: os.cpu_count() - 1)

    Returns:
        Tuple of (results_list, effective_max_workers_used)
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed

    if not papers:
        return [], 0

    def process_single_paper(paper: dict) -> Dict:
        """Process a single paper for affiliation extraction."""
        try:
            paper_title = paper.get("title", "")
            paper_text = paper.get("first_page_text", "")

            if not paper_title or not paper_text:
                logging.warning(f"Skipping paper with missing title or text: {paper_title}")
                return {
                    "input_tokens": 0,
                    "output_tokens": 0,
                    "affiliations": None,
                    "error": "Missing title or first_page_text"
                }

            result = getAffiliations(
                paper_title=paper_title,
                paper_text=paper_text,
                provider=provider,
                api_key=api_key,
                base_url=base_url,
                model=model
            )
            return result

        except Exception as e:
            logging.error(f"Error processing paper '{paper.get('title', 'Unknown')}': {e}")
            return {
                "input_tokens": 0,
                "output_tokens": 0,
                "affiliations": None,
                "error": str(e)
            }

    # Use ThreadPoolExecutor to process papers concurrently
    # Set max_workers: default to cpu_count - 1, with reasonable limits for API calls
    if max_workers is None:
        cpu_count = os.cpu_count()
        max_workers = max(1, cpu_count - 1) if cpu_count else 4
    max_workers = min(max_workers, len(papers), 10)  # Cap at 10 to avoid overwhelming APIs

    results: List[Dict] = [{}] * len(papers)  # Pre-allocate results list to maintain order

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks and map them to their original indices
        future_to_index = {
            executor.submit(process_single_paper, paper): i
            for i, paper in enumerate(papers)
        }

        # Collect results as they complete with progress tracking
        with tqdm(total=len(papers), desc="Processing affiliations", unit="paper") as pbar:
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                    # Update progress bar with paper title if available
                    paper_title = papers[index].get('title', f'Paper {index+1}')
                    pbar.set_postfix_str(f"Completed: {paper_title[:50]}...")
                except Exception as e:
                    logging.error(f"Unexpected error in thread for paper {index}: {e}")
                    results[index] = {
                        "input_tokens": 0,
                        "output_tokens": 0,
                        "affiliations": None,
                        "error": f"Thread execution error: {str(e)}"
                    }
                    pbar.set_postfix_str(f"Error: {papers[index].get('title', f'Paper {index+1}')[:50]}...")
                finally:
                    pbar.update(1)

    return results, max_workers
