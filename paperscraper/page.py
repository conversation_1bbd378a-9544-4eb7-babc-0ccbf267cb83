"""
Utilities for retrieving the first page text for an arXiv paper.

Public API:
    getFirstPageText(paper: dict, char_limit: int = 1500, cache_path: str | None = None) -> dict
        Returns {"source": "html"|"pdf"|"none", "text": str, "error": Optional[str]}

The `paper` dict is expected to have keys:
- abs_url (e.g., https://arxiv.org/abs/2401.01234)
- pdf_url (e.g., https://arxiv.org/pdf/2401.01234)
"""
from __future__ import annotations

import os
import re
from io import BytesIO
from typing import Dict, Optional

import requests
from bs4 import BeautifulSoup
from PyPDF2 import PdfReader


def _get_arxiv_id(paper: dict) -> Optional[str]:
    abs_url = paper.get("abs_url")
    if not abs_url:
        return None
    try:
        return abs_url.rstrip("/").split("/")[-1]
    except Exception:
        return None


def getHTMLFirstPageText(paper: dict, char_limit: int = 1500) -> str:
    arxiv_id = _get_arxiv_id(paper)
    if not arxiv_id:
        return ""
    html_url = f"https://arxiv.org/html/{arxiv_id}"

    response = requests.get(html_url, timeout=30)
    response.raise_for_status()

    soup = BeautifulSoup(response.content, "html.parser")
    text = soup.get_text()

    text = re.sub(r"[ \t]+", " ", text)
    text = re.sub(r"\n+", "\n", text)

    return text[:char_limit]



def getPDFFirstPageText(paper: dict, char_limit: int = 1500) -> str:
    pdf_url = paper.get("pdf_url")
    if not pdf_url:
        # Fall back to constructing from abs id
        arxiv_id = _get_arxiv_id(paper)
        if not arxiv_id:
            return ""
        pdf_url = f"https://arxiv.org/pdf/{arxiv_id}.pdf"

    response = requests.get(pdf_url, timeout=30)
    response.raise_for_status()

    pdf = PdfReader(BytesIO(response.content))
    if not pdf.pages:
        return ""
    text = pdf.pages[0].extract_text() or ""
    return text[:char_limit]


def getFirstPageText(paper: dict, char_limit: int = 1500, cache_path: str | None = None) -> Dict[str, Optional[str]]:
    """Try HTML first; on 404 fall back to PDF; otherwise return none.

    If cache_path is provided and text was extracted, write it there.
    """
    try:
        text = getHTMLFirstPageText(paper, char_limit)
        source = "html"
    except requests.exceptions.HTTPError as e:
        if e.response is not None and e.response.status_code == 404:
            text = getPDFFirstPageText(paper, char_limit)
            source = "pdf"
        else:
            return {"source": "none", "text": "", "error": str(e)}
    except requests.exceptions.RequestException as e:
        return {"source": "none", "text": "", "error": str(e)}

    if cache_path and text:
        try:
            os.makedirs(os.path.dirname(cache_path), exist_ok=True)
            with open(cache_path, "w", encoding="utf-8") as f:
                f.write(text)
        except Exception:
            pass

    return {"source": source, "text": text}
