# paperscraper

A comprehensive tool for collecting arXiv papers: fetch metadata, cache first-page content, and extract author affiliations using LLMs (OpenAI or Ollama). All data is organized in a unified JSON structure for easy analysis.

## Features

- **Metadata Collection**: Fetch arXiv papers by category and date
- **Content Extraction**: Download and parse first-page text from papers
- **Affiliation Analysis**: Extract author affiliations using AI (OpenAI GPT or local Ollama models)
- **Unified Workflow**: Process metadata, pages, and affiliations in a single command
- **Flexible Data Structure**: All data stored in organized JSON format with deduplication
- **Multiple LLM Providers**: Support for both OpenAI API and local Ollama models

## Requirements

- **Python**: 3.9 or higher
- **Dependencies**: Automatically installed via `pyproject.toml`
  - openai>=1.0.0
  - requests>=2.31.0
  - beautifulsoup4>=4.12.0
  - PyPDF>3.0.0
  - certifi>=2023.0.0
  - dotenv
- **For OpenAI**: API key from [OpenAI Platform](https://platform.openai.com/api-keys)
- **For Ollama**: Local Ollama server running at `http://localhost:11434` with a pulled model

## Quick Start

### 1. Installation

Install the package in development mode:

```bash
pip install -e .
```

This installs paperscraper with two command aliases:
- `paperscraper` (full name)
- `pscraper` (short alias)

### 2. Environment Setup

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your preferred settings:

```bash
# Required for OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Override default models (gpt-5-nano for OpenAI, qwen3:4b-instruct for Ollama)
# AFFILIATIONS_MODEL=gpt-4

# Optional: Custom base URLs (defaults shown)
OPENAI_BASE_URL=https://api.openai.com/v1
OLLAMA_BASE_URL=http://localhost:11434/v1
```

### 3. Basic Usage

Collect papers for a specific date and category:

```bash
# Process all steps (metadata + pages + affiliations) for cs.AI papers
paperscraper cs.AI 2024-01-15

# Limit to 5 papers and use verbose output
paperscraper cs.AI 2024-01-15 --num 5 -v

# Use Ollama instead of OpenAI
paperscraper cs.AI 2024-01-15 --provider ollama --model qwen3:4b-instruct
```

### 4. Data Structure

All data is stored in a unified structure under the `data/` directory:

```
data/
└── cs.AI/
    └── 24/
        └── 01/
            └── 15/
                └── papers.json  # All data in one file
```

Each `papers.json` contains:
- Paper metadata (title, authors, abstract, etc.)
- First page text content
- Extracted affiliations
- Processing statistics and token usage

## Detailed Usage Guide

### Command Syntax

The main command follows this pattern:

```bash
paperscraper <category> <date> [options]
```

**Parameters:**
- `<category>`: arXiv category (e.g., `cs.AI`, `cs.CV`, `cs.CL`, `cs.LG`, `math.CO`, `physics.optics`)
- `<date>`: Date in `YYYY-MM-DD` format

### Command Options

#### Processing Control
```bash
--steps metadata,pages,affiliations    # Specify which steps to run (default: all)
--num 10                              # Limit number of papers to process
-y, --yes                             # Auto-confirm processing all papers
--data-dir /path/to/data              # Custom data directory (default: ./data)
```

#### LLM Configuration
```bash
--provider openai                     # Use OpenAI API (default)
--provider ollama                     # Use local Ollama
--model gpt-4                         # Specify model name
--api-key YOUR_KEY                    # Override API key
--base-url https://custom.api.com/v1  # Custom API endpoint
```

#### Logging
```bash
-v                                    # Verbose output (INFO level)
-vv                                   # Very verbose (DEBUG level)
```

### Example Commands

#### Basic Usage
```bash
# Process cs.AI papers for a specific date with OpenAI
paperscraper cs.AI 2024-01-15

# Process computer vision papers
paperscraper cs.CV 2024-01-15 --num 10

# Process computational linguistics papers with verbose output
paperscraper cs.CL 2024-01-15 --num 5 -v

# Process machine learning papers, auto-confirm all
paperscraper cs.LG 2024-01-15 -y

# Process mathematics papers (combinatorics)
paperscraper math.CO 2024-01-15 --num 3
```

#### Step-by-Step Processing
```bash
# Only fetch metadata for computer vision papers
paperscraper cs.CV 2024-01-15 --steps metadata

# Fetch metadata and pages for AI papers (no affiliations)
paperscraper cs.AI 2024-01-15 --steps metadata,pages

# Only process affiliations for existing ML papers
paperscraper cs.LG 2024-01-15 --steps affiliations

# Process multiple categories with different steps
paperscraper cs.AI 2024-01-15 --steps metadata --num 10
paperscraper cs.CV 2024-01-15 --steps metadata --num 10
paperscraper cs.CL 2024-01-15 --steps metadata --num 10
```

#### Using Ollama (Local LLM)
```bash
# First, ensure Ollama is running and model is pulled
ollama pull qwen3:4b-instruct

# Then run paperscraper with Ollama for different categories
paperscraper cs.AI 2024-01-15 --provider ollama --model qwen3:4b-instruct -v
paperscraper cs.CV 2024-01-15 --provider ollama --model qwen3:4b-instruct --num 5
```

#### Custom Configuration
```bash
# Use custom data directory and model for physics papers
paperscraper physics.optics 2024-01-15 \
  --data-dir /custom/path \
  --provider openai \
  --model gpt-3.5-turbo \
  --num 20

# Process multiple categories with different configurations
paperscraper cs.AI 2024-01-15 --provider openai --model gpt-4 --num 10
paperscraper math.CO 2024-01-15 --provider ollama --model llama2 --num 5
```

### LLM Providers

#### OpenAI
- **Base URL**: Configurable via `OPENAI_BASE_URL` env var (default: `https://api.openai.com/v1`) or `--base-url`
- **Authentication**: Requires `OPENAI_API_KEY` environment variable
- **Default Model**: `gpt-5-nano` (override with `AFFILIATIONS_MODEL` env var or `--model`)
- **Recommended Models**: `gpt-5-nano`, `gpt-4`, `gpt-3.5-turbo`
- **Features**: High-quality affiliation extraction, token usage tracking

#### Ollama
- **Base URL**: Configurable via `OLLAMA_BASE_URL` env var (default: `http://localhost:11434/v1`) or `--base-url`
- **Authentication**: No API key required (local)
- **Setup**: Requires Ollama server running locally
- **Default Model**: `qwen3:4b-instruct` (override with `AFFILIATIONS_MODEL` env var or `--model`)
- **Recommended Models**: `qwen3:4b-instruct`, `llama2`, `mistral`
- **Features**: Privacy-focused, no external API calls, free usage

### Supported Categories

The tool supports **all arXiv categories**. You can use any valid arXiv category identifier. If unsure, check the [arXiv category taxonomy](https://arxiv.org/category_taxonomy).

### Data Organization

The tool organizes all data in a hierarchical structure:

```
data/
├── cs.AI/           # Artificial Intelligence papers
│   ├── 24/          # Year (YY format)
│   │   ├── 01/      # Month (MM format)
│   │   │   ├── 15/  # Day (DD format)
│   │   │   │   └── papers.json  # Unified data file
│   │   │   └── 16/
│   │   │       └── papers.json
│   │   └── 02/
├── cs.CV/           # Computer Vision papers
│   └── 24/
│       └── 01/
│           └── 15/
│               └── papers.json
├── cs.CL/           # Computational Linguistics papers
├── cs.LG/           # Machine Learning papers
├── math.CO/         # Mathematics - Combinatorics papers
└── physics.optics/ # Physics - Optics papers
```

**Important Limitation:**
- **Storage by Query Category**: Papers are stored in the directory of the category you query, **not their primary category**. A paper with primary category `cs.CV` but also tagged with `cs.AI` will be stored in `data/cs.AI/` when queried via `paperscraper cs.AI`. This means the same paper may appear in multiple category directories if queried separately.

### Processing Statistics

The tool provides detailed statistics including:
- **Token Usage**: Input/output tokens per paper and total
- **Processing Time**: Time spent on each step
- **Cost Estimation**: Estimated API costs (for OpenAI)
- **Success Rate**: Papers successfully processed vs. failed
- **Source Information**: Whether content came from HTML or PDF

### Advanced Features

#### Organization Detection
The tool can identify notable organizations in author affiliations using the `orgs.txt` file:
- Contains 68+ major AI/ML organizations
- Customizable list for specific research domains
- Helps filter papers by institutional affiliation

#### Automatic Dependency Resolution
The tool automatically adds missing processing steps:
- Running `--steps affiliations` will automatically include `metadata` and `pages` if needed
- Running `--steps pages` will automatically include `metadata` if needed
- Prevents errors from missing dependencies

#### Deduplication
- Automatically deduplicates papers based on arXiv ID
- Safe to run multiple times on the same date
- Preserves existing data while adding new papers

#### Category-Based Storage Behavior

**Important**: Papers are stored based on the **query category**, not their primary arXiv category. This has several implications:

**How it works:**
- When you run `paperscraper cs.AI 2024-01-15`, the tool queries arXiv for all papers that include `cs.AI` in their category list
- Papers are stored in `data/cs.AI/24/01/15/papers.json` regardless of their primary category
- Each paper's metadata includes both `primary_category` and `categories` fields for reference

**Example scenario:**
```json
{
  "title": "Computer Vision with AI Techniques",
  "primary_category": "cs.CV",
  "categories": ["cs.CV", "cs.AI", "cs.LG"],
  "stored_in": "data/cs.AI/24/01/15/papers.json"
}
```

**Implications:**
- **Cross-category papers**: A paper may appear in multiple category directories if queried separately
- **Primary vs. query category**: The directory doesn't necessarily reflect the paper's primary classification
- **Data analysis**: When analyzing results, check the `primary_category` field for the paper's main classification
- **Deduplication**: The same paper won't be duplicated within a single category directory, but may exist in multiple category directories

**Best practices:**
- Use the `primary_category` and `categories` fields in your analysis rather than relying on directory structure
- Be aware that category directories represent "papers found when querying this category" rather than "papers primarily classified in this category"
- Consider this behavior when doing cross-category analysis to avoid double-counting papers

### Troubleshooting

#### Common Issues

**"No papers found for that date"**
- Check if the date format is correct (`YYYY-MM-DD`)
- Verify the category exists and has papers for that date
- Try a different date or category

**"Failed to get page text"**
- Some papers may have restricted access or parsing issues
- The tool will continue processing other papers
- Check verbose output (`-v`) for detailed error messages

**"OpenAI API error"**
- Verify your `OPENAI_API_KEY` is set correctly
- Check your API quota and billing status
- Try using a different model with `--model`

**"Ollama connection failed"**
- Ensure Ollama is running: `ollama serve`
- Verify the model is pulled: `ollama pull qwen3:4b-instruct`
- Check if the base URL is correct with `--base-url`

#### Performance Tips

- Use `--num` to limit papers for testing
- Start with smaller batches before processing full days
- Use Ollama for cost-free processing of large datasets
- Monitor token usage with verbose output (`-v`)

#### Testing New Categories

When trying a new category for the first time:

```bash
# Test with a small number first
paperscraper physics.optics 2024-01-15 --steps metadata --num 2 -v

# Check if papers were found
ls data/physics.optics/24/01/15/

# View the results
head -20 data/physics.optics/24/01/15/papers.json
```

If no papers are found for a specific date/category combination, try:
- A different date (some categories have fewer daily submissions)
- Check the [arXiv category taxonomy](https://arxiv.org/category_taxonomy) for correct category names
- Use verbose output (`-v`) to see detailed API responses
