!pip freeze

!pip install pypdf dotenv

!pip3 install -e ..


import os
from concurrent.futures import ThreadPoolExecutor

def show_max_workers_info():
    """Display max_workers information"""
    
    print("=== Max Workers Information ===\n")
    
    # Show CPU count
    cpu_count = os.cpu_count()
    print(f"CPU cores detected: {cpu_count}")
    
    # Show default calculation
    if cpu_count:
        default_max_workers = max(1, cpu_count - 1)
        print(f"Default max_workers (CPU cores - 1): {default_max_workers}")
    else:
        default_max_workers = 4
        print(f"Default max_workers (fallback): {default_max_workers}")
    
    # Show ThreadPoolExecutor default
    with ThreadPoolExecutor() as executor:
        print(f"ThreadPoolExecutor default max_workers: {executor._max_workers}")
    
    # Show with explicit values
    test_values = [1, 2, 4, 8, None]
    
    print(f"\n=== Testing different max_workers values ===")
    for test_val in test_values:
        if test_val is None:
            # Simulate our logic
            if cpu_count:
                actual_val = max(1, cpu_count - 1)
            else:
                actual_val = 4
            print(f"max_workers=None -> {actual_val} (our default logic)")
        else:
            print(f"max_workers={test_val} -> {test_val}")

if __name__ == "__main__":
    show_max_workers_info()



import json
from pypdf import PdfReader
from io import BytesIO
import requests


# Open and load the metadata.json file
with open('data/cs.AI/24/08/01/papers.json', 'r', encoding='utf-8') as f:
    metadata = json.load(f)

# Access the data
print(f"Date: {metadata['date']}")
print(f"Category: {metadata['category']}")
print(f"Paper count: {metadata['count']}\n---")

# Example: iterate through papers
for paper in metadata['papers']:
    print(f"Title: {paper['title']}")
    print(f"Authors: <AUTHORS>
    print(f"Abstract URL: {paper['abs_url']}")
    print("---")
    break

print(f"{len(metadata['papers'])-1} more papers...")

import requests
from bs4 import BeautifulSoup
import re
from io import BytesIO
from pypdf import PdfReader

def getHTMLFirstPageText(paper, char_limit=1500):
    arxiv_id = paper["abs_url"].split("/")[-1]
    html_url = f"https://arxiv.org/html/{arxiv_id}"
    
    response = requests.get(html_url, timeout=30)
    response.raise_for_status()  # Raises HTTPError for 4xx/5xx

    soup = BeautifulSoup(response.content, 'html.parser')
    text = soup.get_text()

    text = re.sub(r'[ \t]+', ' ', text)
    text = re.sub(r'\n+', '\n', text)

    return text[:char_limit]

def getPDFFirstPageText(paper, char_limit=1500):
    pdf_url = paper["pdf_url"]
    response = requests.get(pdf_url, timeout=30)
    response.raise_for_status()

    pdf = PdfReader(BytesIO(response.content))
    text = pdf.pages[0].extract_text()
    return text[:char_limit]

def getFirstPageText(paper, char_limit=1500):
    arxiv_id = paper["abs_url"].split("/")[-1]
    
    try:
        # Try HTML first
        return {
            "source": "html",
            "text": getHTMLFirstPageText(paper, char_limit)
        }
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            # print(f"HTML page not found for {arxiv_id} (404), falling back to PDF")
            return {
                "source": "pdf",
                "text": getPDFFirstPageText(paper, char_limit),
                "error": e
            }
        else:
            # print(f"HTTP error for {arxiv_id}: {e}")
            return {
                "source": "none",
                "text": "",
                "error": e
            }
    except requests.exceptions.RequestException as e:
        # print(f"Request error for {arxiv_id}: {e}")
        return {
                "source": "none",
                "text": "",
                "error": e
            }


from pydantic import BaseModel
from typing import List, Optional
from openai import OpenAI
import os


import dotenv
dotenv.load_dotenv()


class AuthorAffiliation(BaseModel):
    author_name: str
    organization: str

class AllAuthorAffiliation(BaseModel):
    author_name: str
    organization: str
    is_notable: bool

class PaperAffilationAnalysis(BaseModel):
    paper_title: str
    all_affiliations: List[AllAuthorAffiliation]
    notable_affiliations: List[AuthorAffiliation]
    has_notable_affiliations: bool

ollama_api_base = "http://localhost:11434/v1"
openai_api_base = "https://api.openai.com/v1"

# client = OpenAI(
#     base_url =  openai_api_base,
#     api_key = os.environ["OPENAI_API_KEY"]
#     )

client = OpenAI(
    base_url =  ollama_api_base,
    api_key = "ollama"
    )




def getAffiliations(paper_title, paper_text):
    # Load organizations from file
    with open('orgs.txt', 'r') as f:
        valid_organizations = f.read().strip()

    prompt = f"""
You are tasked with extracting ALL author affiliations from a research paper and determining which ones match the provided notable organizations.

Paper title: {paper_title}

First page text: {paper_text}

Notable organizations to check against:
{valid_organizations}

Extract ALL author affiliations from the paper, then:
1. Mark each affiliation as notable (is_notable: true) if it matches any organization in the provided list
2. Include all affiliations in all_affiliations
3. Include only notable affiliations in notable_affiliations  
4. Set has_notable_affiliations to true if any affiliations match the notable organizations list

Return comprehensive affiliation data for all authors.
"""

    response = client.chat.completions.parse(
        # model="gpt-5-nano",
        model = "gemma3:4b",
        messages=[
            {"role": "system", "content": "Extract ALL author affiliations from research papers and identify which match notable organizations."},
            {
                "role": "user", 
                "content": prompt
                }
        ],
        response_format=PaperAffilationAnalysis,
    )

    return response


paper_index = 1
paper_title = metadata['papers'][paper_index]['title']
paper_text = getFirstPageText(metadata['papers'][paper_index])["text"]

response = getAffiliations(paper_title, paper_text)
# response["affiliations"]
response

response.usage.prompt_tokens
response.usage.completion_tokens

ob = response.choices[0].message.parsed
ob.model_dump()

from tqdm import tqdm
import time


affiliations = []

papers = metadata['papers'][:10]

for paper_index in tqdm(range(len(papers))):
    
    print(f"Processing paper {paper_index} of {len(papers)}: {papers[paper_index]['title']}")

    total_start = time.time()
    paper_title = metadata['papers'][paper_index]['title']
    get_first_page_text_response = getFirstPageText(metadata['papers'][paper_index])
    paper_text = get_first_page_text_response["text"]
    paper_source = get_first_page_text_response["source"]
    first_page_end = time.time()

    response = getAffiliations(paper_title, paper_text)

    total_end = time.time()

    record = {
        **metadata['papers'][paper_index],
        "affiliations": response["affiliations"],
        "input_tokens": response["input_tokens"],
        "output_tokens": response["output_tokens"],
        "total_processing_time": total_end - total_start,
        "first_page_processing_time": first_page_end - total_start,
        "affiliation_processing_time": total_end - first_page_end,
        "paper_source": paper_source
    }

    affiliations.append(record)
    






def getAverageInputTokens(affiliations):
    total = 0
    for affiliation in affiliations:
        total += affiliation["input_tokens"]
    # to 2 decimal places
    return round(total / len(affiliations), 2)

def getAverageOutputTokens(affiliations):
    total = 0
    for affiliation in affiliations:
        total += affiliation["output_tokens"]
    
    # to 2 decimal places
    return round(total / len(affiliations), 2)

def getAverageTotalProcessingTime(affiliations):
    total = 0
    for affiliation in affiliations:
        total += affiliation["total_processing_time"]

    # to 2 decimal places
    return round(total / len(affiliations), 2)


def getAverageFirstPageProcessingTime(affiliations):
    total = 0
    for affiliation in affiliations:
        total += affiliation["first_page_processing_time"]
    # to 4 decimal places
    return round(total / len(affiliations), 2)


def getAverageAffiliationProcessingTime(affiliations):
    total = 0
    for affiliation in affiliations:
        total += affiliation["affiliation_processing_time"]
    return round(total / len(affiliations),2)

def getSourceCounts(affiliations):
    counts = {}
    for affiliation in affiliations:
        source = affiliation["paper_source"]
        if source in counts:
            counts[source] += 1
        else:
            counts[source] = 1
    return counts

def getAverageTokensPerSource(affiliations):
    # gets average input and output tokens per source
    source_counts = getSourceCounts(affiliations)
    source_input_tokens = {}
    source_output_tokens = {}
    
    for affiliation in affiliations:
        source = affiliation["paper_source"]
        source_input_tokens[source] = source_input_tokens.get(source, 0) + affiliation["input_tokens"]
        source_output_tokens[source] = source_output_tokens.get(source, 0) + affiliation["output_tokens"]
    
    # Calculate averages
    source_avg_input = {}
    source_avg_output = {}
    for source in source_counts:
        source_avg_input[source] = round(source_input_tokens[source] / source_counts[source], 2)
        source_avg_output[source] = round(source_output_tokens[source] / source_counts[source], 2)
    
    return source_avg_input, source_avg_output

def getCost(affiliations, price_per_input_token=0.05/1000000, price_per_output_token=0.4/1000000):
    total_cost = 0
    for affiliation in affiliations:
        total_cost += affiliation["input_tokens"] * price_per_input_token + affiliation["output_tokens"] * price_per_output_token
    # to 4 decimal places
    return round(total_cost, 6)

def printStatistics(affiliations):
    print("Number of affiliations: ",len(affiliations))
    print("Average input tokens: ",getAverageInputTokens(affiliations))
    print("Average output tokens: ",getAverageOutputTokens(affiliations))
    print("Average total processing time: ",getAverageTotalProcessingTime(affiliations))
    print("Average first page processing time: ",getAverageFirstPageProcessingTime(affiliations))
    print("Average affiliation processing time: ",getAverageAffiliationProcessingTime(affiliations))
    print("Source counts: ")
    for source in getSourceCounts(affiliations):
        print(f"\t{source} \t{getSourceCounts(affiliations)[source]}")

    # print each source, average input tokens, output tokens on new line and tab
    source_avg_input, source_avg_output = getAverageTokensPerSource(affiliations)
    print("Average tokens per source:")
    for source in source_avg_input:
        print(f"\t{source}\t{source_avg_input[source]}\t{source_avg_output[source]}")

    print("Total cost: ",getCost(affiliations))

printStatistics(affiliations)